#include "impl.h"
#include <ctkPluginContext.h>
#include <QtDebug>


SerialPortImpl::SerialPortImpl()
    : port_(io_service_), is_open_(false)
{
}

SerialPortImpl::~SerialPortImpl()
{
    if (is_open_) {
        closePort();
    }
}

QStringList SerialPortImpl::getAvailablePorts()
{
    QStringList port_names;
    foreach(const QSerialPortInfo &info, QSerialPortInfo::availablePorts())
    {
        port_names << info.portName();
        qDebug() << "Available serial port:" << info.portName();
    }
    return port_names;
}

std::shared_ptr<SerialService> SerialPortImpl::createSerialPort()
{
    return std::make_shared<SerialPortImpl>();
}

bool SerialPortImpl::openPort(const QString& port_name, int baud_rate, int data_bits)
{
    // 如果已经打开，先关闭
    if (is_open_) {
        closePort();
    }

    try {
        // 打开串口
        port_.open(port_name.toStdString());

        if (port_.is_open()) {
            qDebug() << "Serial port" << port_name << "opened successfully";

            // 设置串口参数
            port_.set_option(boost::asio::serial_port_base::baud_rate(baud_rate));
            port_.set_option(boost::asio::serial_port_base::character_size(data_bits));
            port_.set_option(boost::asio::serial_port_base::parity(boost::asio::serial_port_base::parity::none));
            port_.set_option(boost::asio::serial_port_base::stop_bits(boost::asio::serial_port_base::stop_bits::one));
            port_.set_option(boost::asio::serial_port_base::flow_control(boost::asio::serial_port_base::flow_control::none));

            is_open_ = true;
            return true;
        } else {
            qDebug() << "Failed to open serial port:" << port_name;
            return false;
        }
    } catch (const boost::system::system_error& e) {
        qDebug() << "Error opening serial port" << port_name << ":" << e.what();
        return false;
    }
}

bool SerialPortImpl::closePort()
{
    if (!is_open_) {
        return true;  // 已经关闭
    }

    try {
        port_.close();
        is_open_ = false;
        qDebug() << "Serial port closed successfully";
        return true;
    } catch (const boost::system::system_error& e) {
        qDebug() << "Error closing serial port:" << e.what();
        return false;
    }
}

bool SerialPortImpl::sendData(const QString& data)
{
    if (!is_open_) {
        qDebug() << "Serial port is not open";
        return false;
    }

    try {
        size_t bytes_transferred = boost::asio::write(
            port_,
            boost::asio::buffer(data.toStdString())
        );
        qDebug() << "Sent" << bytes_transferred << "bytes";
        return true;
    } catch (const boost::system::system_error& e) {
        qDebug() << "Error sending data:" << e.what();
        return false;
    }
}

bool SerialPortImpl::readData(QString& data)
{
    if (!is_open_) {
        qDebug() << "Serial port is not open";
        return false;
    }

    char read_buffer[128] = {0};

    try {
        size_t bytes_transferred = port_.read_some(
            boost::asio::buffer(read_buffer)
        );

        qDebug() << "Read" << bytes_transferred << "bytes";

        std::string received_data(read_buffer, bytes_transferred);
        data = QString::fromStdString(received_data);
        return true;
    } catch (const boost::system::system_error& e) {
        qDebug() << "Error reading data:" << e.what();
        return false;
    }
}

bool SerialPortImpl::isPortOpen()
{
    return is_open_;
}
