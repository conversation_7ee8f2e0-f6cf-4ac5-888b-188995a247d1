#ifndef MAINCONTROLLER_H
#define MAINCONTROLLER_H

#include "CIMConnect.h"
#include <QObject>
#include <QProcess>
#include <QPixmap>
#include <QString>
#include <atomic>
#include <condition_variable>
#include <memory>
#include <mutex>
#include <string>
#include <thread>
#include <variant>
#include <vector>

#include "AsynTCPClient.h"
#include "ZmqClient.h"
#include "ctkPluginContext.h"

// 服务头文件
#include "../DataManager/src/service.h"
#include "../ImageUI/src/service.h"
#include "../Light/src/service.h"
#include "../LogUI/src/service.h"
#include "../MidImage/src/service.h"
#include "../ModbusTcp/src/ModbusTcpService.h"
#include "../VisionAlgo/src/service.h"
#include"../qserial/src/service.h"

/**
 * @brief 消息结构体
 */
struct CtkMessage {
    QString key;
    QString value;
};

/**
 * @brief 主控制器类
 */
class MainController : public QObject {
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param context 插件上下文
     * @param parent 父对象
     */
    explicit MainController(ctkPluginContext* context, QObject* parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~MainController();

    // ================ 通信相关方法 ================

    /**
     * @brief 发送消息
     * @param message 消息内容
     */
    void sendMessage(QString message);

    /**
     * @brief 发送中间消息
     * @param msg 消息结构体
     */
    void sendMessageMid(const CtkMessage& msg);

    /**
     * @brief 发送图像
     * @param img 图像数据
     */
    void sendImageMid(const cv::Mat& img);

    /**
     * @brief 连接Cimetrix
     */
    void ConnectCimetrix();

    /**
     * @brief 执行远程命令
     * @param command 命令
     * @param paramName 参数名称列表
     * @param paramValue 参数值列表
     * @param ptr 命令结果指针
     */
    void RemoteCommand(const std::string& command,
                       const std::vector<std::string>& paramName,
                       const std::vector<std::variant<bool, int, double, std::string>>& paramValue,
                       CommandResults* ptr);

    /**
     * @brief 终端消息处理
     * @param message 消息列表
     */
    void TerminalMessage(const std::vector<std::string>& message) const;

    /**
     * @brief 变量改变处理
     * @param variableName 变量名
     * @param newValue 新值
     */
    void VariableChanged(const std::string& variableName,
                         const std::variant<bool, int, double, std::string>& newValue) const;

    /**
     * @brief 通信状态改变处理
     * @param state 状态
     */
    void CommunicationStateChange(long state) const;

    /**
     * @brief 控制状态改变处理
     * @param state 状态
     */
    void ControlStateChange(long state) const;

    /**
     * @brief 缓冲状态改变处理
     * @param state 状态
     */
    void SpoolingStateChange(long state) const;

    /**
     * @brief 状态改变处理
     * @param name 名称
     * @param state 状态
     */
    void StateChange(const std::string& name, long state) const;

    /**
     * @brief 接收消息处理
     * @param mes 消息
     */
    void ReceiveMessage(const Message& mes);

    /**
     * @brief 注册回调
     * @param id 标识符
     */
    void RegisterCallBack(const std::string& id);

    /**
     * @brief 连接RFID
     */
    void RFID_Connect();

    // ================ 设备控制方法 ================

    /**
     * @brief 连接Modbus
     * @return 是否成功
     */
    bool ConnectToModbus();

    /**
     * @brief 断开Modbus连接
     * @return 是否成功
     */
    bool DisConnectModbus();

    /**
     * @brief 关闭光源
     * @return 是否成功
     */
    bool CloseLight();

    /**
     * @brief 打开光源
     * @return 是否成功
     */
    bool OpenLight();

    /**
     * @brief 连接光源
     * @return 是否成功
     */
    bool ConnectLight();

    /**
     * @brief 打开相机
     * @return 是否成功
     */
    bool OpenCamera();

    /**
     * @brief 关闭相机
     * @return 是否成功
     */
    bool CloseCamera();

    /**
     * @brief 获取图像
     * @return 是否成功
     */
    bool GetImage();

    // ================ 运动控制方法 ================

    /**
     * @brief DD轴右移
     * @return 是否成功
     */
    bool DDRMove();

    /**
     * @brief DD轴左移
     * @return 是否成功
     */
    bool DDLMove();

    /**
     * @brief DD轴右停
     * @return 是否成功
     */
    bool DDRStop();

    /**
     * @brief DD轴左停
     * @return 是否成功
     */
    bool DDLStop();

    /**
     * @brief Z1轴右移
     * @return 是否成功
     */
    bool Z1RMove();

    /**
     * @brief Z1轴左移
     * @return 是否成功
     */
    bool Z1LMove();

    /**
     * @brief Z1轴右停
     * @return 是否成功
     */
    bool Z1RStop();

    /**
     * @brief Z1轴左停
     * @return 是否成功
     */
    bool Z1LStop();

    // ================ 流程控制方法 ================

    /**
     * @brief 开始初始化流程
     * @return 是否成功
     */
    bool StartInitProcess();

    /**
     * @brief 开始标定流程
     * @return 是否成功
     */
    bool StartCalibrationProcess();

    /**
     * @brief 开始检测流程
     * @return 是否成功
     */
    bool StartDetection();

    /**
     * @brief 查找缺口测试
     * @return 是否成功
     */
    bool FindNorchTest();

    // ================ 辅助方法 ================

    /**
     * @brief 记录日志
     * @param level 日志级别
     * @param message 日志消息
     */
    void Log(const QString& level, const QString& message);

    /**
     * @brief 生成SLA命令字符串
     * @param value1 参数1
     * @param value2 参数2
     * @return 命令字符串
     */
    QString generateSLAString(int value1, int value2);

signals:
    /**
     * @brief 更新标签信号
     */
    void updateLabels(short initMotionStart, short detectionMotionStart, short zAxisMotionStart,
                      short ddRotationStart, short initMotionEnd, short detectionMotionEnd,
                      short zAxisMotionInPosition, short ddRotationInPosition,
                      short motionStatusCheckZ1, short motionStatusCheckZ2,
                      short motionStatusCheckDD,
                      short vacuumStatusCheck, short materialSuctionAlarm,
                      short ingotPlacementError, int Zposition, int DDposition, int height, short isputwafer);

    /**
     * @brief 显示图像信号
     */
    void showimage(QPixmap pixmap);

    /**
     * @brief 博客发布信号
     */
    void blogPublished(const ctkDictionary&);

public:
    std::shared_ptr<LogUIService> log_ui_;
    std::shared_ptr<ImageUIService> image_ui_service_;
    std::shared_ptr<EdgeLocatorParam> param_;  // 参数指针

private:
    // ================ 私有方法 ================

    /**
     * @brief 检测缺口流程
     * @return 流程状态
     */
    int DetectNorchProcess();

    /**
     * @brief 初始化流程
     * @return 流程状态
     */
    int InitProcess();

    /**
     * @brief 检测缺口
     * @return 检测结果
     */
    int detectNorch();

    /**
     * @brief 移动到相机位置
     * @return 移动结果
     */
    int MoveToCamPos();

    /**
     * @brief Modbus刷新线程
     */
    void ModbusRefreshThread();

    /**
     * @brief 注册服务
     */
    void RegServer();

    /**
     * @brief 注册发布
     */
    void Registerpublish();

    /**
     * @brief 主循环
     */
    void MainLoop();

    /**
     * @brief 启动通信中间件
     */
    void StartCommunicationMiddleware();

    /**
     * @brief 杀死服务
     * @param service_name 服务名称
     */
    void KillService(const QString& service_name);

    /**
     * @brief 检查端口是否使用
     * @param port 端口号
     * @param pid 进程ID
     * @return 是否使用
     */
    bool isPortInUse(int port, QString& pid);

    /**
     * @brief 杀死进程
     * @param pid 进程ID
     */
    void killProcess(const QString& pid);

    /**
     * @brief 检测AGV卸载
     * @return 是否成功
     */
    bool upload();

    /**
     * @brief 检测AGV加载
     * @return 是否成功
     */
    bool load();

    // ================ 服务对象 ================
    std::shared_ptr<ModbusTcpService> modbus_tcp_server_;
    std::shared_ptr<DataMangerService> data_manager_service_;
    std::shared_ptr<VisionAlgoService> vision_algo_service_;
    std::shared_ptr<MidImageService> mid_image_service_;
    std::shared_ptr<LightService> light_service_;




    // ================ 通信对象 ================
    std::unique_ptr<CIMConnect> cim_connect_ptr_;  // CIMConnect
    std::shared_ptr<AsynTCPClient> asyn_tcp_client_;  // RFID通讯
    std::shared_ptr<SerialService>serial_service_;
    std::shared_ptr<SerialService> serial_port_;  // 串口对象
    QProcess* communication_middleware_;  // 通信中间件

    // ================ 线程相关 ================
    std::thread worker_thread_;
    std::thread modbus_thread_;
    std::atomic<bool> keep_running_{true};
    std::atomic<bool> keep_running_modbus_{true};
    std::atomic<bool> alarm_flags_{false};
    std::condition_variable condition_variable_;  // 等待远程命令回复条件变量
    std::mutex mutex_;  // 等待远程命令回复互斥量

    // ================ 状态变量 ================
    std::atomic<bool> receive_reply_;  // 是否收到远程命令回复
    int remote_command_result_;  // 远程命令执行结果
    int detect_norch_step_{0};  // 检测流程步骤
    int move_to_cam_pos_step_{0};  // 相机标定流程步骤
    int main_loop_step_{0};  // 主循环流程步骤
    int init_step_{0};  // 初始化流程步骤

    // ================ 图像处理相关 ================
    cv::Mat image_;  // 检测图像
    double angle_{0.0};  // 识别角度
    double pre_angle_{0.0};  // 上次识别角度
    double plc_angle_{0.0};  // PLC运动角度
    double plc_move_angle_{0.0};  // PLC需要运动角度
    double total_plc_angle_{0.0};  // 累加角度
    bool is_ok_{false};
    int count_{0};  // 检测成功次数

    // ================ 其他 ================
    ctkPluginContext* context_;
};

#endif // MAINCONTROLLER_H
