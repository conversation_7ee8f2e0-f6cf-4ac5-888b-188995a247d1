﻿#pragma once

#include <QtPlugin>
#include <QString>
#include <QStringList>
#include <memory>

/**
 * @brief 串口服务接口
 *
 */
class SerialService
{
public:
    virtual ~SerialService() {}

    /**
     * @brief 获取可用串口名称列表
     * @return 可用串口名称列表
     */
    virtual QStringList getAvailablePorts() = 0;

    /**
     * @brief 创建新的串口对象
     * @return 新创建的串口服务对象
     */
    virtual std::shared_ptr<SerialService> createSerialPort() = 0;

    /**
     * @brief 打开串口连接
     * @param port_name 串口名称
     * @param baud_rate 波特率
     * @param data_bits 数据位
     * @return 成功返回true，失败返回false
     */
    virtual bool openPort(const QString& port_name, int baud_rate, int data_bits) = 0;

    /**
     * @brief 关闭串口连接
     * @return 成功返回true，失败返回false
     */
    virtual bool closePort() = 0;

    /**
     * @brief 发送数据到串口
     * @param data 要发送的数据
     * @return 成功返回true，失败返回false
     */
    virtual bool sendData(const QString& data) = 0;

    /**
     * @brief 从串口读取数据
     * @param data 接收数据的变量
     * @return 成功返回true，失败返回false
     */
    virtual bool readData(QString& data) = 0;

    /**
     * @brief 检查串口连接是否打开
     * @return 已打开返回true，否则返回false
     */
    virtual bool isPortOpen() = 0;
};

#define SerialService_iid "org.commontk.service.demos.SerialService"
Q_DECLARE_INTERFACE(SerialService, SerialService_iid)
