#include "passivestatemachine.h"
#include <stdexcept>
#include <iostream>
#include <QtCore/QString>

// 常量定义
namespace {
    constexpr int kOn = 1;
    constexpr int kOff = 0;
    constexpr const char* kDefaultPortName = "COM3";
}

PassiveStateMachine::PassiveStateMachine(std::shared_ptr<SerialService> serial_port,
                                       const std::string& port_name,
                                       int baud_rate,
                                       int data_bits)
    : current_state_(State::kIdle)
    , serial_port_(serial_port->createSerialPort())
    , port_name_(port_name.empty() ? kDefaultPortName : port_name)
    , baud_rate_(baud_rate)
    , data_bits_(data_bits)
{
    InitializeSerialPort();
}

PassiveStateMachine::~PassiveStateMachine()
{
    if (serial_port_ && serial_port_->isPortOpen()) {
        serial_port_->closePort();
    }
}

std::string PassiveStateMachine::GetCurrentStateName() const
{
    switch(current_state_) {
        case State::kIdle: return "IDLE";
        case State::kWaitingForAgvRequestCmd: return "WAITING_FOR_AGV_REQUEST_CMD";
        case State::kPreparingTransfer: return "PREPARING_TRANSFER";
        case State::kReadyWaitingForAgvBusy: return "READY_WAITING_FOR_AGV_BUSY";
        case State::kMonitoringTransfer: return "MONITORING_TRANSFER";
        case State::kWaitingForFinalAgvCleanup: return "WAITING_FOR_FINAL_AGV_CLEANUP";
        case State::kErrorState: return "ERROR_STATE";
        default: return "UNKNOWN_STATE";
    }
}
void PassiveStateMachine::Reset()
{
    current_state_ = State::kIdle;
    adam_data_.Reset();
}

bool PassiveStateMachine::InitializeSerialPort()
{
    if (!serial_port_) {
        HandleError("Serial port object is null");
        return false;
    }

    bool result = serial_port_->openPort(QString::fromStdString(port_name_), baud_rate_, data_bits_);
    if (!result) {
        HandleError("Failed to open serial port: " + port_name_);
        return false;
    }

    std::cout << "Serial port opened successfully: " << port_name_ << std::endl;
    return true;
}

std::vector<bool> PassiveStateMachine::HexByteStringTo8Bits(const std::string& hex_byte_str) {
    if (hex_byte_str.length() != 2) {
        throw std::invalid_argument("Hex byte string must be 2 characters long. Got: '" + hex_byte_str + "'");
    }

    unsigned int byte_value;
    try {
        byte_value = std::stoul(hex_byte_str, nullptr, 16);
    } catch (const std::exception& e) {
        throw std::invalid_argument("Invalid hex character in string: '" + hex_byte_str + "' (" + e.what() + ")");
    }

    if (byte_value > 0xFF) {
        throw std::out_of_range("Hex value out of 8-bit range: '" + hex_byte_str + "'");
    }

    std::vector<bool> bits(kMaxChannels);
    for (int i = 0; i < kMaxChannels; ++i) {
        bits[i] = (byte_value >> i) & 1;
    }
    return bits;
}

PassiveStateMachine::Adam4055Data PassiveStateMachine::ParseAdam4055Response(const std::string& response_str_with_cr) {
    Adam4055Data result;
    result.raw_response = response_str_with_cr;

    if (response_str_with_cr.empty()) {
        result.error_message = "Response string is empty.";
        return result;
    }

    std::string response_str = response_str_with_cr;
    // 移除末尾的回车符 '\r'
    if (response_str.back() == '\r') {
        response_str.pop_back();
    }

    if (response_str.empty()) {
        result.error_message = "Response string became empty after removing CR.";
        return result;
    }

    char indicator = response_str[0];

    // 检查响应头和基本长度
    if ((indicator != '!' && indicator != '?') || response_str.length() < 3) {
        result.error_message = "Invalid response format: Must start with '!' or '?' and contain an address. Got: '" + response_str + "'";
        return result;
    }

    result.module_address = response_str.substr(1, 2);

    if (indicator == '?') {
        result.is_error_response = true;
        result.command_was_valid = false;
        result.error_message = "Module indicated an invalid command or error.";
        return result;
    }

    // 处理 '!' 开头的有效响应
    result.command_was_valid = true;
    // 预期数据部分在 "!AA" 之后，长度应为 (dataOutput:2) + (dataInput:2) + (suffix:2) = 6 个字符
    // 例如：!01(DataOut)(DataIn)00
    if (response_str.length() != 1 + 2 + 2 + 2 + 2) { // 1(!) + 2(AA) + 2(DO) + 2(DI) + 2(00) = 9
        result.error_message = "Invalid response length for ADAM-4055. Expected 9 chars before CR (e.g., !AADOODII00). Got: '" + response_str + "'";
        // 仍然尝试解析已知部分
    }

    std::string data_part = response_str.substr(3); // 获取 "!AA" 之后的部分

    if (data_part.length() < 4) { // 至少需要DO和DI数据
        result.error_message = (result.error_message.empty() ? "" : result.error_message + " ") + "Data part too short for DO and DI fields. Data part: '" + data_part + "'";
        return result;
    }

    try {
        // 解析 (dataOutput) - 2位十六进制字符代表8个DO
        result.output_states = HexByteStringTo8Bits(data_part.substr(0, 2));

        // 解析 (dataInput) - 2位十六进制字符代表8个DI
        result.input_states = HexByteStringTo8Bits(data_part.substr(2, 2));

        // 检查后缀 "00"
        if (data_part.length() >= 6) {
            if (data_part.substr(4, 2) != "00") {
                std::string warning = "Warning: Expected suffix '00', got '" + data_part.substr(4, 2) + "'.";
                result.error_message = result.error_message.empty() ? warning : result.error_message + " " + warning;
            }
        } else {
             std::string warning = "Warning: Suffix '00' is missing or data part is too short.";
             result.error_message = result.error_message.empty() ? warning : result.error_message + " " + warning;
        }

    } catch (const std::exception& e) {
        result.error_message = (result.error_message.empty() ? "" : result.error_message + " ") + "Exception during data parsing: " + e.what();
    }

    return result;
}

// Adam4055Data结构体方法实现
void PassiveStateMachine::Adam4055Data::Print() const {
    std::cout << "--- Parsed ADAM-4055 Response ---" << std::endl;
    std::cout << "Raw Response: '" << raw_response << "'" << std::endl;
    if (!error_message.empty()) {
        std::cout << "Error: " << error_message << std::endl;
    }
    std::cout << "Module Address: " << module_address << std::endl;
    std::cout << "Command Valid (indicated by '!'): " << (command_was_valid ? "Yes" : "No") << std::endl;
    std::cout << "Is Error Response (indicated by '?'): " << (is_error_response ? "Yes" : "No") << std::endl;

    if (command_was_valid && !is_error_response) {
        if (!output_states.empty()) {
            std::cout << "Output States (DO0 to DO7): ";
            for (size_t i = 0; i < output_states.size(); ++i) {
                std::cout << "CH" << i << "=" << (output_states[i] ? "ON " : "OFF ");
            }
            std::cout << std::endl;
        }
        if (!input_states.empty()) {
            std::cout << "Input States (DI0 to DI7): ";
            for (size_t i = 0; i < input_states.size(); ++i) {
                std::cout << "CH" << i << "=" << (input_states[i] ? "HIGH " : "LOW ");
            }
            std::cout << std::endl;
        }
    }
    std::cout << "---------------------------------" << std::endl;
}

void PassiveStateMachine::Adam4055Data::Reset() {
    command_was_valid = false;
    is_error_response = false;
    module_address.clear();
    output_states.clear();
    input_states.clear();
    raw_response.clear();
    error_message.clear();
}

bool PassiveStateMachine::Adam4055Data::IsValid() const {
    return command_was_valid && !is_error_response && !output_states.empty() && !input_states.empty();
}

bool PassiveStateMachine::IsAdamOutputAck(const std::string& response_str) {
    if (response_str.empty()) {
        return false;
    }

    std::string temp_response = response_str;

    // 检查并移除末尾可能存在的回车符 '\r' (CR)
    if (temp_response.back() == '\r') {
        temp_response.pop_back();
    }

    // 移除回车符后，剩下的字符串应该仅仅是 ">"
    return temp_response == ">";
}

bool PassiveStateMachine::GetIoState() {
    if (!serial_port_) {
        HandleError("Serial port is null");
        return false;
    }

    QString response;
    bool ret = serial_port_->sendData(QString::fromStdString(kAdamQueryCommand));
    if (!ret) {
        HandleError("Failed to send query command");
        return false;
    }

    ret = serial_port_->readData(response);
    if (!ret) {
        HandleError("Failed to read response data");
        return false;
    }

    adam_data_ = ParseAdam4055Response(response.toStdString());
    adam_data_.Print();
    return adam_data_.IsValid();
}

std::string PassiveStateMachine::FormatOutputCommand(OutputSignal signal, int state) {
    std::string signal_str;
    switch (signal) {
        case OutputSignal::kLReq:   signal_str = "#01100"; break;
        case OutputSignal::kUReq:   signal_str = "#01110"; break;
        case OutputSignal::kNc1:    signal_str = "#01120"; break;
        case OutputSignal::kReady:  signal_str = "#01130"; break;
        case OutputSignal::kNc2:    signal_str = "#01140"; break;
        case OutputSignal::kNc3:    signal_str = "#01150"; break;
        case OutputSignal::kHoAvbl: signal_str = "#01160"; break;
        case OutputSignal::kEs:     signal_str = "#01170"; break;
        default:
            HandleError("Unknown output signal");
            return "";
    }
    return signal_str + (state == kOn ? "1" : "0") + "\r";
}

bool PassiveStateMachine::SetLReq(int state) {
    if (!serial_port_) {
        HandleError("Serial port is null");
        return false;
    }

    QString response;
    std::string command = FormatOutputCommand(OutputSignal::kLReq, state);
    bool ret = serial_port_->sendData(QString::fromStdString(command));
    if (!ret) {
        HandleError("Failed to send L_REQ command");
        return false;
    }

    ret = serial_port_->readData(response);
    if (!ret) {
        HandleError("Failed to read L_REQ response");
        return false;
    }

    ret = IsAdamOutputAck(response.toStdString());
    if (!ret) {
        HandleError("Invalid L_REQ acknowledgment");
        return false;
    }

    return true;
}

bool PassiveStateMachine::SetReady(int state) {
    if (!serial_port_) {
        HandleError("Serial port is null");
        return false;
    }

    QString response;
    std::string command = FormatOutputCommand(OutputSignal::kReady, state);
    bool ret = serial_port_->sendData(QString::fromStdString(command));
    if (!ret) {
        HandleError("Failed to send READY command");
        return false;
    }

    ret = serial_port_->readData(response);
    if (!ret) {
        HandleError("Failed to read READY response");
        return false;
    }

    ret = IsAdamOutputAck(response.toStdString());
    if (!ret) {
        HandleError("Invalid READY acknowledgment");
        return false;
    }

    return true;
}

void PassiveStateMachine::HandleError(const std::string& error_message) {
    std::cout << "Error: " << error_message << std::endl;
    current_state_ = State::kErrorState;
}

bool PassiveStateMachine::ProcessStateLogic()
{
    switch (current_state_)
    {
    case State::kIdle:
        if (GetIoState())
        {
            if (adam_data_.input_states[static_cast<int>(InputSignal::kValid)] &&
                adam_data_.input_states[static_cast<int>(InputSignal::kCs0)])
            {
                current_state_ = State::kWaitingForAgvRequestCmd;
                std::cout << "State transition: IDLE -> WAITING_FOR_AGV_REQUEST_CMD" << std::endl;
            }
        }
        else
        {
            HandleError("Failed to get IO state in IDLE");
            return false;
        }
        break;

    case State::kWaitingForAgvRequestCmd:
        if (SetLReq(kOn))
        {
            if (GetIoState())
            {
                if (adam_data_.input_states[static_cast<int>(InputSignal::kTrReq)])
                {
                    current_state_ = State::kPreparingTransfer;
                    std::cout << "State transition: WAITING_FOR_AGV_REQUEST_CMD -> PREPARING_TRANSFER" << std::endl;
                }
                else
                {
                    HandleError("TR_REQ signal not received");
                    return false;
                }
            }
            else
            {
                HandleError("Failed to get IO state after setting L_REQ");
                return false;
            }
        }
        else
        {
            HandleError("Failed to set L_REQ signal");
            return false;
        }
        break;

    case State::kPreparingTransfer:
        if (SetReady(kOn))
        {
            if (GetIoState())
            {
                if (adam_data_.input_states[static_cast<int>(InputSignal::kBusy)])
                {
                    current_state_ = State::kMonitoringTransfer;
                    std::cout << "State transition: PREPARING_TRANSFER -> MONITORING_TRANSFER" << std::endl;
                }
                else
                {
                    HandleError("BUSY signal not received");
                    return false;
                }
            }
            else
            {
                HandleError("Failed to get IO state after setting READY");
                return false;
            }
        }
        else
        {
            HandleError("Failed to set READY signal");
            return false;
        }
        break;

    case State::kMonitoringTransfer:
        if (SetLReq(kOff))
        {
            if (GetIoState())
            {
                if (adam_data_.input_states[static_cast<int>(InputSignal::kBusy)] &&
                    adam_data_.input_states[static_cast<int>(InputSignal::kCompt)] &&
                    adam_data_.input_states[static_cast<int>(InputSignal::kTrReq)])
                {
                    current_state_ = State::kWaitingForFinalAgvCleanup;
                    std::cout << "State transition: MONITORING_TRANSFER -> WAITING_FOR_FINAL_AGV_CLEANUP" << std::endl;
                }
                else
                {
                    HandleError("Required signals not received for transfer completion");
                    return false;
                }
            }
            else
            {
                HandleError("Failed to get IO state during transfer monitoring");
                return false;
            }
        }
        else
        {
            HandleError("Failed to turn off L_REQ signal");
            return false;
        }
        break;

    case State::kWaitingForFinalAgvCleanup:
        if (SetReady(kOff))
        {
            if (GetIoState())
            {
                if (adam_data_.input_states[static_cast<int>(InputSignal::kCompt)] &&
                    adam_data_.input_states[static_cast<int>(InputSignal::kValid)] &&
                    adam_data_.input_states[static_cast<int>(InputSignal::kCs0)])
                {
                    current_state_ = State::kIdle;
                    std::cout << "State transition: WAITING_FOR_FINAL_AGV_CLEANUP -> IDLE" << std::endl;
                    std::cout << "Transfer cycle completed successfully" << std::endl;
                }
                else
                {
                    HandleError("Final cleanup signals not received");
                    return false;
                }
            }
            else
            {
                HandleError("Failed to get IO state during final cleanup");
                return false;
            }
        }
        else
        {
            HandleError("Failed to turn off READY signal");
            return false;
        }
        break;

    case State::kErrorState:
        std::cout << "State machine is in error state. Manual intervention required." << std::endl;
        return false;

    default:
        HandleError("Unknown state encountered");
        return false;
    }

    return true;
}