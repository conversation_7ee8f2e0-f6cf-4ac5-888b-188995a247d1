#include "maincontroller.h"
#include"DefineType.h"
#include "service/event/ctkEventAdmin.h"




MainController::MainController(ctkPluginContext* context, QObject *parent)
    : QObject{parent}
    ,context_(context)
{
    //注册服务
    RegServer();
    //启动所有线程
    worker_thread_ = std::thread(&MainController::MainLoop, this);//缺口检测线程
    modbus_thread_ = std::thread(&MainController::ModbusRefreshThread, this);//状态检测线程

}

MainController::~MainController()
{
    // 停止循环
    keep_running_ = false;
    if (worker_thread_.joinable()) {
        worker_thread_.join();
    }

    keep_running_modbus_ = false;
    if (modbus_thread_.joinable()) {
        modbus_thread_.join();
    }
	//cim_connect_ptr_->Shutdown();

    if (communication_middleware_ != nullptr)
    {
        communication_middleware_->write("quit");
        communication_middleware_->closeWriteChannel();
        communication_middleware_->waitForFinished();
        delete communication_middleware_;
        communication_middleware_ = nullptr;
    }
}

///
/// \brief MainController::MainLoop所有操作主流程
///
void MainController::MainLoop()
{
    //case 1:初始化变量
    //case 100:检测子流程
    //case 200:初始化子流程
    //case 300:标定子流程
    //case 999:异常退出
    //case 1000:正常推出

    while(keep_running_)
    {
        switch (main_loop_step_) {
        case 1:
            break;
        case 100:
        {
            int ret = DetectNorchProcess();
            if(ret==SUCCESSED)
            {
                detect_norch_step_ = 0;
                main_loop_step_ = 1000;

            }
            else if(ret == FAILED)
            {
                detect_norch_step_ = 0;
                main_loop_step_ = 999;
            }
            break;
        }
        case 200:
        {
            int ret = InitProcess();
            if(ret == SUCCESSED)
            {
                init_step_ = 0;
                main_loop_step_ = 1000;
            }
            else if(ret == FAILED)
            {
                init_step_ = 0;
                main_loop_step_ = 999;
            }
            break;
        }
        case 300:
        {
            int ret = MoveToCamPos();
            if(ret == SUCCESSED)
            {
                Log("info","MoveToCamPos SUCCESSED");
                move_to_cam_pos_step_ = 0;
                main_loop_step_ = 1000;
            }
            else if(ret == FAILED)
            {
                Log("info","MoveToCamPos FAILED");
                move_to_cam_pos_step_ = 0;
                main_loop_step_ = 999;
            }
            break;
        }
        case 999:
            main_loop_step_ = 0;
            break;
        case 1000:
            main_loop_step_ = 0;
            break;
        }
        Sleep(20);
    }
}

///
/// \brief MainController::Registerpublish发送事件
///
void MainController::Registerpublish()
{
    ctkServiceReference ref = context_->getServiceReference<ctkEventAdmin>();
    if (ref) {
        ctkEventAdmin* eventAdmin = context_->getService<ctkEventAdmin>(ref);
        // 使用 Qt::DirectConnection 等同于 ctkEventAdmin::sendEvent()
        eventAdmin->publishSignal(this, SIGNAL(blogPublished(ctkDictionary)), "org/commontk/logManager", Qt::QueuedConnection);
    }
}


///
/// \brief MainController::DetectNorchProcess检测流程函数
///
int MainController:: DetectNorchProcess() {
    //step10:Z轴下降计算晶锭厚度
    //step20:运动到实际拍摄高度
    //step30:加载模版
    //step40:图像采集
    //step50:算法检测
    //step60:旋转指定角度
    //step999:异常结束
    //step1000:正常结束

    switch (detect_norch_step_)
    {
    case 1:
        {
            Log("info","检测开始");
            modbus_tcp_server_->WriteShortValue(REG_DETECTION_MOTION_START,MOTION_END);
            modbus_tcp_server_->WriteShortValue(REG_IS_PUT_WAFER,MOTION_END);
            modbus_tcp_server_->WriteShortValue(REG_Z_AXIS_MOTION_START,MOTION_END);
            modbus_tcp_server_->WriteShortValue(REG_DD_ROTATION_START,MOTION_END);
            // int baudRate = param_->lightSourceControllerParam.linkParameter.baud;
            // QString COM = param_->lightSourceControllerParam.linkParameter.Com;
            // int dataBits = param_->lightSourceControllerParam.linkParameter.nByteSize;

            // light_service_->Open(COM,baudRate,dataBits);
            is_ok_ = false;
            angle_ = 0.0;//识别角度
            pre_angle_ = 0.0;//上次识别角度
            plc_angle_ = 0.0;//PLC运动角度
            plc_move_angle_ = 0.0;//PLC需要运动角度
            total_plc_angle_ = 0.0;//累加角度
            detect_norch_step_ = 10;
        }
        break;
    case 10:
        if(modbus_tcp_server_ != Q_NULLPTR)
        {
            Log("info","上料前检测开始");
            //检测流程开始
            modbus_tcp_server_->WriteShortValue(REG_DETECTION_MOTION_START,MOTION_START);
            detect_norch_step_ = 15;
        }
        break;
    case 15:
        if(modbus_tcp_server_ != Q_NULLPTR)
        {
            short isput;
            Sleep(500);//等待真空状态建立
            //流程开始真空先吸附一下 判断是否有锭
            modbus_tcp_server_->ReadShortValue(REG_INGOT_PLACEMENT,isput);
            //手动放锭
            if(!isput)
            {
                Log("info","上料前检测完成");
                Sleep(15000);
                //开始检测运动流程
                modbus_tcp_server_->WriteShortValue(REG_IS_PUT_WAFER,MOTION_START);
                detect_norch_step_ = 16;
            }
            else
            {
                Log("WARN","上料前检测到晶锭");
                detect_norch_step_ = 999;
            }
        }
        break;
    case 16:
    {
        short isput;
        //判断夹爪下降完成
        modbus_tcp_server_->ReadShortValue(REG_Z2_AXIS_MOTION_IN_POS,isput);
        if(isput)
        {
            // LOG
            // Sleep(500);//夹爪下降后需要等待
            detect_norch_step_ = 20;
        }
        break;
    }
    case 20:
        if(modbus_tcp_server_ != Q_NULLPTR)
        {
            short moveend;
            modbus_tcp_server_->ReadShortValue(REG_DETECTION_MOTION_END,moveend);
            if(moveend)
            {
                Log("info","运动到起始位置开始");
                modbus_tcp_server_->WriteShortValue(REG_IS_PUT_WAFER,MOTION_END);
                int pos;
                //计算运动高度
                //获取测高高度
                int cegao = 0;
                int cegaoZ = 0;
                modbus_tcp_server_->ReadIntValue(REG_HEIGHT,cegao);
                modbus_tcp_server_->ReadIntValue(REG_Z_POSITION,cegaoZ);
                // //获取陶瓷盘高度
                // int chuck =-2478260;
                // //获取相机标定高度
                // int camera = -1259867;
                int chuck = param_->initialHeight*10000;
                int camera = param_->cameraWorkingHeight*10000;
                pos = cegaoZ - chuck + cegao + camera;
                modbus_tcp_server_->WriteIntValue(Z_AXIS_WORKING_HEIGHT,pos);
                //运动到指定Z轴高度
                modbus_tcp_server_->WriteShortValue(REG_Z_AXIS_MOTION_START,MOTION_START);
                detect_norch_step_ = 25;
            }
            if(alarm_flags_)  // 当走到这一步的时候没有经过上面的if，读取线程中alarm_flags_的值还未改变，之后直接走报错那一段
            {
                Log("info","alarm_flags_ == 1");
                detect_norch_step_ = 999;
            }
        }
        break;
    case 25:
        if(modbus_tcp_server_ != Q_NULLPTR)
        {
            short moveend;
            modbus_tcp_server_->ReadShortValue(REG_Z1_AXIS_MOTION_IN_POS,moveend);
            if(moveend)
            {
                Log("info","运动到起始位置结束");
                modbus_tcp_server_->WriteShortValue(REG_Z_AXIS_MOTION_START,MOTION_END);
                detect_norch_step_ = 30;
            }
        }

        break;
    case 30:
        //开光源
        Log("info","光源开启");
        OpenLight();
        Log("info","光源开启完成");
        detect_norch_step_ = 40;
        break;
    case 40:
        // if(camera_server_ != Q_NULLPTR)
        // {
        //     Log("info","打开相机");
        //     if(camera_server_->Open(param_->mCameraParam.CameraID))
        //     {
        //         Log("info","设置相机参数");
        //         camera_server_->SetExposureTime(param_->mCameraParam.ExposureTime);
        //         camera_server_->SetReverseX(param_->mCameraParam.ReverseX);
        //         camera_server_->SetReverseY(param_->mCameraParam.ReverseY);
        //         detect_norch_step_ = 50;

        //     }
        //     else
        //     {
        //         Log("info","打开相机失败");
        //         detect_norch_step_ = 999;
        //     }
        // }
        // else
        // {
        //     detect_norch_step_ = 999;
        // }
        // break;

        if(image_ui_service_ != Q_NULLPTR)
        {
            OpenCamera();   // 每次前面会黑一下就是因为打开会传一张黑图进去
            detect_norch_step_ = 50;
        }
        else
        {
            Log("info", "camera_server_ == Q_NULLPTR");
            detect_norch_step_ = 999;
        }
        break;

    case 50:
        GetImage();
        detect_norch_step_ = 60;
        break;
    case 60:
    {
        Log("info","缺口检测开始");
        double tempAngle =0.0;
        cv::Mat dst;
        double source = param_->matchingscore;
        if(vision_algo_service_->FindEdge(image_,dst,source,tempAngle))
        {
            // 此时需要返回中间件矩阵数据后再图像界面进行显示（利用事件的方式）
            sendImageMid(dst);
            Log("info",QString("检测到缺口角度: %1").arg(tempAngle));

            //QPixmap pixmap = vision_algo_service_->Mat2QPixmap(dst);
            //emit showimage(pixmap);
            pre_angle_ = angle_;
            angle_ = tempAngle;
            detect_norch_step_ = 70;//判断是否为初始角度
        }
        else
        {
            Log("info","未检测到缺口");
            //QPixmap pixmap = vision_algo_service_->Mat2QPixmap(dst);
            //emit showimage(pixmap);
            count_ = 0;
            plc_move_angle_ = 10;
            detect_norch_step_ = 100;//运动指定角度 10度

        }
        break;
    }
    case 70:
        if(count_ > 0)
        {
            detect_norch_step_ = 80;
        }
        else
        {
            pre_angle_ = angle_;
            count_++;
            plc_move_angle_ = 3;
            detect_norch_step_ = 100;
        }
        break;
    case 80:
        if(abs(pre_angle_ - angle_) - plc_move_angle_ < 0.5)
        {
            count_++;
            if(count_ > 3)//检测成功
            {
                double rotateangle = 360 - angle_;
                //旋转效率优化
                if(rotateangle > 180)
                {
                    rotateangle = rotateangle - 360;
                }
                if(modbus_tcp_server_ != Q_NULLPTR)
                {
                    modbus_tcp_server_->WriteIntValue(DD_MOTOR_ANGLE, 500 * rotateangle);
                    modbus_tcp_server_->WriteShortValue(REG_DD_ROTATION_START, MOTION_START);
                    is_ok_ = true;
                }
                detect_norch_step_ = 110;//等待旋转完成
            }
            else
            {
                plc_move_angle_ = 3;
                detect_norch_step_ = 100;//运动指定角度 3度
            }
        }
        else
        {
            //识别异常 重新识别
            count_ = 0;
            pre_angle_ = 0;
            plc_move_angle_ = 10;
            detect_norch_step_ = 100;//运动指定角度 10度
        }
        break;
    case 100:
        total_plc_angle_ += plc_move_angle_;
        if(total_plc_angle_ > 360)
        {
            detect_norch_step_ = 999;
        }
        else
        {
            if(modbus_tcp_server_ != Q_NULLPTR)
            {
                //plc_move_angle_ = 360;
                modbus_tcp_server_->WriteIntValue(DD_MOTOR_ANGLE, 500 * plc_move_angle_);
                modbus_tcp_server_->WriteShortValue(REG_DD_ROTATION_START, MOTION_START);
                //运动指定角度
                detect_norch_step_ = 110;
            }
        }
        break;
    case 110:
        if(modbus_tcp_server_ != Q_NULLPTR)
        {
            short moveend;
            modbus_tcp_server_->ReadShortValue(REG_DD_ROTATION_IN_POS, moveend);
            if(moveend)
            {
                //运动指令复位
                modbus_tcp_server_->WriteShortValue(REG_DD_ROTATION_START, MOTION_END);
                if(is_ok_)
                {
                    //检测完成拍照确认角度(获得相机拍摄图片)
                    GetImage();
                    cv::Mat dst;
                    double tempAngle;
                    double source = param_->matchingscore;
                    vision_algo_service_->FindEdge(image_, dst, source, tempAngle); // 经过畸变矫正的图进行寻边定位
                    sendImageMid(dst);
                    Log("info", QString("确认缺口角度: %1").arg(tempAngle));
                    detect_norch_step_ = 1000;//流程结束
                }
                else
                {
                    detect_norch_step_ = 50;//再次拍照
                }
            }
        }
        break;
    case 999:
        Log("info","缺口检测流程异常结束");
        modbus_tcp_server_->WriteShortValue(REG_DETECTION_MOTION_START,MOTION_END);//检测流程结束
        is_ok_ = false;
        CloseLight();
        detect_norch_step_ = 0;
        return FAILED;
    case 1000:
        Log("info","缺口检测流程正常结束");
        modbus_tcp_server_->WriteShortValue(REG_DETECTION_MOTION_START,MOTION_END);//检测流程结束
        is_ok_ = false;
        CloseLight();
        detect_norch_step_ = 0;
        return SUCCESSED;
    }
    return BUSYNESS;
}

///
/// \brief GuiSerial::detectNorch缺口检测测试流程
///
int MainController::detectNorch()
{
    cv::Mat image, dst;
    double tempAngle;
    GetImage(); // 通知相机拍照并且显示图片
    mid_image_service_->getImageResult(image); // 导入图片
    vision_algo_service_->FindEdge(image, dst, param_->matchingscore, tempAngle);
    // 此时需要返回中间件矩阵数据后再图像界面进行显示（利用事件的方式）
    sendImageMid(dst);

    Sleep(100);
    return true;
}

///
/// \brief GuiSerial::MoveToCamPos 运动到相机相对高度
///
int MainController::MoveToCamPos()
{
    switch (move_to_cam_pos_step_)
    {
    case 1:
        Log("info","标定流程开始");
        modbus_tcp_server_->WriteShortValue(REG_Z_AXIS_MOTION_START,MOTION_END);
        move_to_cam_pos_step_ = 10;
        break;
    case 10:
        if(modbus_tcp_server_ != Q_NULLPTR)
        {
            Log("info","开始对零");
            //对零开始
            modbus_tcp_server_->WriteShortValue(REG_CALIBRATION_CAMERA_START,MOTION_START);
            move_to_cam_pos_step_ = 20;
        }
        break;
    case 20:
        if(modbus_tcp_server_ != Q_NULLPTR)
        {
            short moveend;
            modbus_tcp_server_->ReadShortValue(REG_CALIBRATION_CAMERA_END,moveend);
            if(moveend)
            {
                Log("info","对零完成");
                int pos;
                //计算运动高度
                //获取测高高度
                int cegao = 0;
                int cegaoZ = 0;
                modbus_tcp_server_->ReadIntValue(REG_HEIGHT,cegao);
                modbus_tcp_server_->ReadIntValue(REG_Z_POSITION,cegaoZ);
                // //获取陶瓷盘高度
                // int chuck =-2478260;
                // //获取相机标定高度
                // int camera = -1259867;
                int chuck = param_->initialHeight*10000;
                int camera = param_->cameraWorkingHeight*10000;
                pos = cegaoZ - chuck + cegao + camera;
                modbus_tcp_server_->WriteIntValue(Z_AXIS_WORKING_HEIGHT,pos);
                //运动到指定Z轴高度
                modbus_tcp_server_->WriteShortValue(REG_Z_AXIS_MOTION_START,MOTION_START);
                move_to_cam_pos_step_ = 25;
            }
        }
        break;
    case 25:
        if(modbus_tcp_server_ != Q_NULLPTR)
        {
            short moveend;
            modbus_tcp_server_->ReadShortValue(REG_Z1_AXIS_MOTION_IN_POS,moveend);
            if(moveend)
            {
                Log("info","运动到标定位置完成");
                modbus_tcp_server_->WriteShortValue(REG_Z_AXIS_MOTION_START,MOTION_END);
                move_to_cam_pos_step_ = 1000;//运动完成
            }
        }
        break;
    case 999:
        Log("WARN","标定流程异常结束");
        modbus_tcp_server_->WriteShortValue(REG_CALIBRATION_CAMERA_START,MOTION_END);
        move_to_cam_pos_step_ = 0;
        return FAILED;
    case 1000:
        Log("info","标定流程正常结束");
        modbus_tcp_server_->WriteShortValue(REG_CALIBRATION_CAMERA_START,MOTION_END);
        move_to_cam_pos_step_ = 0;
        return SUCCESSED;
    }
    return BUSYNESS;
}

bool MainController::ConnectToModbus()
{
    if(modbus_tcp_server_ != Q_NULLPTR)
    {
        // if(!modbus_tcp_server_->Connect())
        // {
        //     Log("info","连接MODBUS失败");
        //     return false;
        // }
        std::shared_ptr<CommunicationParam> paramcomm = std::make_shared<CommunicationParam>(param_->mcommunication);
        modbus_tcp_server_->SetParam(paramcomm);

        //复位寄存器
        modbus_tcp_server_->WriteShortValue(REG_DETECTION_MOTION_START,MOTION_END);
        modbus_tcp_server_->WriteShortValue(REG_IS_PUT_WAFER,MOTION_END);
        modbus_tcp_server_->WriteShortValue(REG_Z_AXIS_MOTION_START,MOTION_END);
    }
    Log("info","连接MODBUS成功");
    return true;
}

bool MainController::DisConnectModbus()
{
    if(modbus_tcp_server_ != Q_NULLPTR)
    {
        modbus_tcp_server_->DisConnect();
    }
    return true;
}

//光源类型转换
QString MainController::generateSLAString(int value1, int value2) {
    // 使用 QString 的格式化功能
    return QString("SLA%1B%2#")
        .arg(value1, 4, 10, QChar('0')) // 将 value1 格式化为4位，前补零
        .arg(value2, 4, 10, QChar('0')); // 将 value2 格式化为4位，前补零
}
bool MainController::CloseLight()
{
    if(light_service_ != Q_NULLPTR)
    {
        if(light_service_->Close()){
            return true;
        }
        else{
            return false;
        }
    }
    return false;
}

// bool MainController::ConnectLight()
// {
//     if(serial_service_ != Q_NULLPTR)
//     {
//         int baudRate = param_->lightSourceControllerParam.linkParameter.baud;
//         QString COM = param_->lightSourceControllerParam.linkParameter.Com;
//         int dataBits = param_->lightSourceControllerParam.linkParameter.nByteSize;

//         serial_service_->openPort(COM, baudRate, dataBits);
//     }
//     return true;
// }

bool MainController::OpenLight()
{
    // if(serial_service_ != Q_NULLPTR)
    // {
    //     int light1 = param_->lightSourceControllerParam.lightValue1;
    //     int light2 = param_->lightSourceControllerParam.lightValue2;
    //     serial_service_->sendData(generateSLAString(light1,light2));
    // }
    // return true;

    if(light_service_ != Q_NULLPTR)
    {
        int light1 = param_->lightSourceControllerParam.lightValue1;
        int light2 = param_->lightSourceControllerParam.lightValue2;

        if(light_service_->SetLight(light1, light2)){
            return true;
        }
        else{
            return false;
        }
    }
    return false;

}

bool MainController::ConnectLight()
{
    int baudRate = param_->lightSourceControllerParam.linkParameter.baud;
    QString COM = param_->lightSourceControllerParam.linkParameter.Com;
    int dataBits = param_->lightSourceControllerParam.linkParameter.nByteSize;

    return light_service_->Open(COM, baudRate, dataBits);
}

bool MainController::OpenCamera()
{
    // if(camera_server_ != Q_NULLPTR)
    // {
    //     camera_server_->Open(param_->mCameraParam.CameraID);
    //     camera_server_->SetExposureTime(param_->mCameraParam.ExposureTime);
    //     camera_server_->SetReverseX(param_->mCameraParam.ReverseX);
    //     camera_server_->SetReverseY(param_->mCameraParam.ReverseY);
    // }

    // image_ui_service_->open(QString::number(param_->mCameraParam.CameraID), QString::number(param_->mCameraParam.ReverseX),
    //                        QString::number(param_->mCameraParam.ReverseY),QString::number(param_->mCameraParam.ExposureTime));

    // 打开摄像机ID
    const CtkMessage mes1{ "ID", QString::number(param_->mCameraParam.CameraID)};
    sendMessageMid(mes1);
    // 打开
    const CtkMessage mes2{ "Open", "true"};
    sendMessageMid(mes2);

    const CtkMessage mes3{ "ReverseX", QString::number(param_->mCameraParam.ReverseX)};
    sendMessageMid(mes3);

    const CtkMessage mes4{ "ReverseY", QString::number(param_->mCameraParam.ReverseY)};
    sendMessageMid(mes4);

    const CtkMessage mes5{ "ExposureTime", QString::number(param_->mCameraParam.ExposureTime)};
    sendMessageMid(mes5);

    Log("info","open Camera");
    return true;
}

bool MainController::CloseCamera()
{
    // m_ImageUIService->close();   // 不在通过ImageUi界面去关闭相机,直接通过事件发送给中间件关闭相机，发送内容和UI界面中相同

    const CtkMessage mes{ "Close", "true"};
    sendMessageMid(mes);
    Log("info","close Camera");
    return true;
}

bool MainController::GetImage()
{
    const CtkMessage mes{ "Send", "mode1"};
    sendMessageMid(mes);

    //cv::Mat image;
    mid_image_service_->getImageResult(image_);
    Log("info","get image");
    return true;
}


///
/// \brief MainController::StartInitProcess开始初始化流程
/// \return
///
bool MainController::StartInitProcess()
{
    Log("info","点击初始化按键");
    if(main_loop_step_==0)
    {
       main_loop_step_ = 200;
       init_step_ = 1;
    }
    return true;
}
///
/// \brief MainController::StartCalibrationProcess开始标定流程
/// \return
///
bool MainController::StartCalibrationProcess()
{
    Log("info","点击标定按键");
    if(main_loop_step_==0)
    {
        main_loop_step_ = 300;
        move_to_cam_pos_step_ = 1;
    }
    return true;
}

bool MainController::StartDetection()
{
    Log("info","点击检测按键");
    if(main_loop_step_==0)
    {
        main_loop_step_ = 100;
        detect_norch_step_ = 1;
    }
    return true;
}

///
/// \brief MainController::FindNorchTest 测试识别函数
/// \return
///
bool MainController::FindNorchTest()
{
    detectNorch();
    return false;
}

bool MainController::DDRMove()
{
    if(modbus_tcp_server_ != Q_NULLPTR)
    {
        modbus_tcp_server_->WriteShortValue(DD_NEGATIVE_MOTION,MOTION_START);
    }
return true;
}
bool MainController::DDRStop()
{
    if(modbus_tcp_server_ != Q_NULLPTR)
    {
        modbus_tcp_server_->WriteShortValue(DD_NEGATIVE_MOTION,MOTION_END);
    }
    return true;
}
bool MainController::DDLMove()
{
    if(modbus_tcp_server_ != Q_NULLPTR)
    {
        modbus_tcp_server_->WriteShortValue(DD_POSITIVE_MOTION,MOTION_START);
    }
    return true;
}
bool MainController::DDLStop()
{
    if(modbus_tcp_server_ != Q_NULLPTR)
    {
        modbus_tcp_server_->WriteShortValue(DD_POSITIVE_MOTION,MOTION_END);
    }
    return true;
}


bool MainController::Z1RMove()
{
    if(modbus_tcp_server_ != Q_NULLPTR)
    {
        modbus_tcp_server_->WriteShortValue(Z1_AXIS_NEGATIVE_MOTION,MOTION_START);
    }
return true;
}
bool MainController::Z1RStop()
{
    if(modbus_tcp_server_ != Q_NULLPTR)
    {
        modbus_tcp_server_->WriteShortValue(Z1_AXIS_NEGATIVE_MOTION,MOTION_END);
    }
    return true;
}
bool MainController::Z1LMove()
{
    if(modbus_tcp_server_ != Q_NULLPTR)
    {
        modbus_tcp_server_->WriteShortValue(Z1_AXIS_NORMAL_MOTION,MOTION_START);
    }
    return true;
}

bool MainController::Z1LStop()
{
    if(modbus_tcp_server_ != Q_NULLPTR)
    {
        modbus_tcp_server_->WriteShortValue(Z1_AXIS_NORMAL_MOTION,MOTION_END);
    }
return true;
}
//注册所有需要的服务
void MainController::RegServer()
{
    // //获取相机服务
    // auto reference = context_->getServiceReference<CameraServer>();
    // auto ref = context_->getService<CameraServer>(reference);
    // m_CameraServer = std::shared_ptr<CameraServer>(ref);

    // 获取 modbusService 的引用
    auto reference = context_->getServiceReference<ModbusTcpService>();
    auto ref1 = context_->getService<ModbusTcpService>(reference);
    modbus_tcp_server_ = std::shared_ptr<ModbusTcpService>(ref1);
    // //获取日志服务
    // reference = context_->getServiceReference<LogService>();
    // auto ref2 = context_->getService<LogService>(reference);
    // m_LogService = std::shared_ptr<LogService>(ref2);
    //获取配置文件服务
    QList<ctkServiceReference> referenceRegs = context_->getServiceReferences<DataMangerService>("(&(name=EdgeLocatorData))");
    auto ref3 = context_->getService<DataMangerService>(referenceRegs[0]);
    data_manager_service_ = std::shared_ptr<DataMangerService>(ref3);
    data_manager_service_->SetIReader();
    param_ = std::static_pointer_cast<EdgeLocatorParam>(data_manager_service_->GetData());
    //data_manager_service_->SetData(param_);

    // //获取串口服务
    // reference = context_->getServiceReference<qserial>();
    // auto ref4 = context_->getService<qserial>(reference);
    // m_qserialService = std::shared_ptr<qserial>(ref4);

    //获取算法服务
    reference = context_->getServiceReference<VisionAlgoService>();
    auto ref5 = context_->getService<VisionAlgoService>(reference);
    vision_algo_service_ = std::shared_ptr<VisionAlgoService>(ref5);

    //获取Log显示插件
    reference = context_->getServiceReference<LogUIService>();
    auto ref6 = context_->getService<LogUIService>(reference);
    log_ui_ = std::shared_ptr<LogUIService>(ref6);

    //获取图像操作服务
    reference = context_->getServiceReference<ImageUIService>();
    auto ref7 = context_->getService<ImageUIService>(reference);
    image_ui_service_ = std::shared_ptr<ImageUIService>(ref7);
    //获取中间件
    reference = context_->getServiceReference<MidImageService>();
    auto ref8 = context_->getService<MidImageService>(reference);
    mid_image_service_ = std::shared_ptr<MidImageService>(ref8);
    //获取光源服务
    reference = context_->getServiceReference<LightService>();
    auto ref9 = context_->getService<LightService>(reference);
    light_service_ = std::shared_ptr<LightService>(ref9);


    // 获取串口服务
    reference = context_->getServiceReference<SerialService>();
    auto service = context_->getService<SerialService>(reference);
    serial_service_ = std::shared_ptr<SerialService>(service);
    // 创建串口对象
    serial_port_ = serial_service_->createSerialPort();


    //发布事件
    Registerpublish();
    //modbus连接
    ConnectToModbus();
    //连接串口
    ConnectLight();
    //连接Cimetrix
    //ConnectCimetrix();暂时不使用,使用zmq来接受消息

    //启动zmq通讯中间件
    communication_middleware_ = new QProcess(this);
    StartCommunicationMiddleware();
    //初始化中间件回调函数
    ZMQ.RegisterMessageProcess(std::bind(&MainController::ReceiveMessage, this, std::placeholders::_1));
    //初始化RFID
    asyn_tcp_client_ = std::make_shared<AsynTCPClient>(param_->mrfid.ip, param_->mrfid.port);//初始化RFID
    asyn_tcp_client_->RegisterCallBack(std::bind(&MainController::RegisterCallBack, this, std::placeholders::_1));//初始化RFID回调函数


}

///
/// \brief MainController::initFunction初始化流程
///
int MainController::InitProcess()
{
    switch (init_step_) {
    case 1:
        if(modbus_tcp_server_ != Q_NULLPTR)
        {
            modbus_tcp_server_->WriteShortValue(REG_INIT_MOTION_START,MOTION_START);
            Log("info","初始化开始");
            init_step_ = 10;
        }
        break;
    case 10:
        if(modbus_tcp_server_ != Q_NULLPTR)
        {
            short end;
            modbus_tcp_server_->ReadShortValue(REG_INIT_MOTION_END,end);
            if(end)
            {
                modbus_tcp_server_->WriteShortValue(REG_INIT_MOTION_START,MOTION_END);
                init_step_ = 1000;
            }
        }
        break;
     case 999:
        Log("info","初始化异常");
        init_step_ = 0;
        return FAILED;
     case 1000:
        Log("info","初始化结束");
        init_step_ = 0;
        return SUCCESSED;
    }
    return BUSYNESS;
}
/// <summary>
/// 获取moddbus的状态,并根据异常问题进行处理
/// </summary>
void MainController::ModbusRefreshThread()
{
    while(keep_running_modbus_)
    {
        if(modbus_tcp_server_ != Q_NULLPTR)
        {
            short initMotionStart;//初始化流程运动开始
            short detectionMotionStart;//检测流程运动开始
            short zAxisMotionStart;//Z轴运动开始
            short ddRotationStart;//DD旋转运动开始
            short initMotionEnd;//初始化流程运动结束
            short detectionMotionEnd ;//检测流程运动结束
            short zAxisMotionInPosition ;//Z轴电运动到位
            short ddRotationInPosition ;//DD旋转运动到位
            short motionStatusCheckZ1 ;//运动状态检测
            short motionStatusCheckZ2 ;//运动状态检测
            short motionStatusCheckDD ;//运动状态检测
            short initStatusCheck = 0;//初始化状态检测
            short vacuumStatusCheck ;//真空状态检测
            short materialSuctionAlarm ;//吸附有料报警，不能上料
            short ingotPlacementError ;//晶锭放置位置异常
            int Zpostion;//Z轴位置
            int DDpostion;//DD位置
            int height;//测距高度
            short isputwafer;//是否存在晶锭


            modbus_tcp_server_->ReadShortValue(REG_INIT_MOTION_START,initMotionStart);
            modbus_tcp_server_->ReadShortValue(REG_DETECTION_MOTION_START,detectionMotionStart);
            modbus_tcp_server_->ReadShortValue(REG_Z_AXIS_MOTION_START,zAxisMotionStart);
            modbus_tcp_server_->ReadShortValue(REG_DD_ROTATION_START,ddRotationStart);
            modbus_tcp_server_->ReadShortValue(REG_INIT_MOTION_END,initMotionEnd);
            modbus_tcp_server_->ReadShortValue(REG_DETECTION_MOTION_END,detectionMotionEnd);
            modbus_tcp_server_->ReadShortValue(REG_Z1_AXIS_MOTION_IN_POS,zAxisMotionInPosition);
            modbus_tcp_server_->ReadShortValue(REG_DD_ROTATION_IN_POS,ddRotationInPosition);
            modbus_tcp_server_->ReadShortValue(REG_MOTION_STATUS_CHECK_Z1,motionStatusCheckZ1);
            modbus_tcp_server_->ReadShortValue(REG_MOTION_STATUS_CHECK_Z2,motionStatusCheckZ2);
            modbus_tcp_server_->ReadShortValue(REG_MOTION_STATUS_CHECK_DD,motionStatusCheckDD);
            //modbus_tcp_server__->ReadShortValue(645,initStatusCheck);

            modbus_tcp_server_->ReadShortValue(REG_VACUUM_STATUS_CHECK,vacuumStatusCheck); // 读取

            modbus_tcp_server_->ReadShortValue(REG_INGOT_PLACEMENT,materialSuctionAlarm);

            modbus_tcp_server_->ReadShortValue(REG_INGOT_PLACEMENT_ERROR,ingotPlacementError);  // 读取

            modbus_tcp_server_->ReadIntValue(REG_Z_POSITION,Zpostion);
            modbus_tcp_server_->ReadIntValue(REG_DD_POSITION,DDpostion);
            modbus_tcp_server_->ReadIntValue(REG_HEIGHT,height);
            modbus_tcp_server_->ReadShortValue(REG_IS_PUT_WAFER,isputwafer);

            if((detect_norch_step_!=0)&&(ingotPlacementError==1||vacuumStatusCheck==0))//在检测流程运行过程中对真空和夹爪传感器判断
            {
                alarm_flags_ = true;
            }
            else
            {
                alarm_flags_ = false;
            }


            // 发射信号更新 UI
            emit updateLabels(initMotionStart, detectionMotionStart, zAxisMotionStart, ddRotationStart,
                              initMotionEnd, detectionMotionEnd, zAxisMotionInPosition, ddRotationInPosition,
                              motionStatusCheckZ1, motionStatusCheckZ2, motionStatusCheckDD,/* initStatusCheck,*/
                              vacuumStatusCheck, materialSuctionAlarm, ingotPlacementError, Zpostion, DDpostion, height,isputwafer);
        }
        Sleep(10);
    }
}

///
/// \brief GuiSerial::Log 日志记录模块
/// \param level
/// \param message
///
void MainController::Log(const QString& level,const QString& message)
{
    ctkDictionary dic;
    dic["sender"] = "Main";
    dic["level"] = level;
    dic["message"] =message;
    emit blogPublished(dic);

}

void MainController::sendMessage(QString message)
{
    if (const ctkServiceReference ref = context_->getServiceReference<ctkEventAdmin>())
    {
        if (ctkEventAdmin* eventAdmin = context_->getService<ctkEventAdmin>(ref))
        {
            ctkDictionary dic;
            dic["sender"] = "Main";

            dic["message"] =message;
            const ctkEvent event("org/commontk/logManager", dic);
            eventAdmin->sendEvent(event);
        }
    }
}

void MainController::sendMessageMid(const CtkMessage& msg){
    if (const ctkServiceReference ref = context_->getServiceReference<ctkEventAdmin>())
    {
        if (ctkEventAdmin* eventAdmin = context_->getService<ctkEventAdmin>(ref))
        {
            ctkDictionary dic;
            dic["sender"] = "ImageUI";
            dic[msg.key] = msg.value;
            const ctkEvent event("org/commontk/ImageUI", dic);
            eventAdmin->sendEvent(event);
        }
    }
}

void MainController::sendImageMid(const cv::Mat& img){
    auto data = reinterpret_cast<long long>(&img);  // 将图片转换为指针后进行传递

    if (const ctkServiceReference ref = context_->getServiceReference<ctkEventAdmin>())
    {
        if (ctkEventAdmin* eventAdmin = context_->getService<ctkEventAdmin>(ref))
        {
            ctkDictionary dic;
            dic["sender"] = "ImageUI";
            dic["content"] = data;
            const ctkEvent event("org/commontk/ImageUI", dic);
            eventAdmin->sendEvent(event);
        }
    }
}

void MainController::ConnectCimetrix()
{
    cim_connect_ptr_ = std::make_unique<CIMConnect>();

    cim_connect_ptr_->RegisterRemoteCommand(std::bind(&MainController::RemoteCommand, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3, std::placeholders::_4));
    cim_connect_ptr_->RegisterTerminalMessage(std::bind(&MainController::TerminalMessage, this, std::placeholders::_1));
    cim_connect_ptr_->RegisterVariableChange(std::bind(&MainController::VariableChanged, this, std::placeholders::_1, std::placeholders::_2));
    cim_connect_ptr_->RegisterCommunicationStateChange(std::bind(&MainController::CommunicationStateChange, this, std::placeholders::_1));
    cim_connect_ptr_->RegisterControlStateChange(std::bind(&MainController::ControlStateChange, this, std::placeholders::_1));
    cim_connect_ptr_->RegisterSpoolingStateChange(std::bind(&MainController::SpoolingStateChange, this, std::placeholders::_1));
    cim_connect_ptr_->RegisterStateChange(std::bind(&MainController::StateChange, this, std::placeholders::_1, std::placeholders::_2));

    cim_connect_ptr_->Initialize("");//初始化

}

void MainController::RemoteCommand(const std::string& command, const std::vector<std::string>& paramName, const std::vector<std::variant<bool, int, double, std::string>>& paramValue, CommandResults* ptr)
{
    //const CommunicationMessage mes{ "RemoteCommand", command, -1, paramName, {}, paramValue };
    //m_upper_computer_protocol_ptr->SendData(mes);
    std::unique_lock locker(mutex_);
    condition_variable_.wait(locker, [this] {return receive_reply_.load();});
    *ptr = static_cast<CommandResults>(remote_command_result_);
    receive_reply_.store(false);
}

void MainController::TerminalMessage(const std::vector<std::string>& message) const
{

}

void MainController::VariableChanged(const std::string& variableName, const std::variant<bool, int, double, std::string>& newValue) const
{

}

void MainController::CommunicationStateChange(long state) const
{
    switch (state)
    {
    case Disabled:
        //mes.name = "Disable";
        break;
    case Enabled:
        //mes.name = "Enabled";
        break;
    case WaitCRAOrCRFromHost:
        //mes.name = "WaitCRAOrCRFromHost";
        break;
    case WaitDelayOrCRFromHost:
        //mes.name = "WaitDelayOrCRFromHost";
        break;
    case Communicating:
        //mes.name = "Communicating";
        break;
    default:
        break;
    }
}

void MainController::ControlStateChange(long state) const
{
    switch (state)
    {
    case DefaultOnline:
        //mes.name = "DefaultOnline";
        break;
    case EqOffline:
        //mes.name = "EqOffline";
        break;
    case AttemptOnline:
        //mes.name = "AttemptOnline";
        break;
    case HostOffline:
        //mes.name = "HostOffline";
        break;
    case OnlineLocal:
        //mes.name = "OnlineLocal";
        break;
    case OnlineRemote:
        //mes.name = "OnlineRemote";
        break;
    default:
        break;
    }
}

void MainController::SpoolingStateChange(long state) const
{
    switch (state)
    {
    case Inactive:
        //mes.name = "Inactive";
        break;
    default:
        //mes.name = "Active";
        break;
    }
}

void MainController::StateChange(const std::string& name, long state) const
{
    //CommunicationMessage mes{ name };
    //if (name == "ControlStateSwitch")
        //mes.name = state == 0 ? "local" : "remote";
    //if (name == "CtrlOnlineSwitch")
        //mes.name = state == 0 ? "offline" : "online";
    //if (name == "CommEnableSwitch")
        //mes.name = state == 0 ? "disabled" : "enabled";
}

/// <summary>
/// 接受远程消息具体处理相关逻辑
/// </summary>
/// <param name="mes">接受的zmq消息</param>
void MainController::ReceiveMessage(const Message& mes)
{

    if (mes.topic == "ControlStateSwitch") {
        // ui->label_5->setText(QString::fromStdString(mes.name));
    }
    else if (mes.topic == "CtrlOnlineSwitch") {
        // ui->label_4->setText(QString::fromStdString(mes.name));
    }
    else if (mes.topic == "CommEnableSwitch") {
        // ui->label_3->setText(QString::fromStdString(mes.name));
    }
    else if (mes.topic == "SpoolState") {
        // ui->label_7->setText(QString::fromStdString(mes.name));
    }
    else if (mes.topic == "CONTROLSTATE") {
        // ui->label_2->setText(QString::fromStdString(mes.name));
    }
    else if (mes.topic == "RemoteCommand") {
        if (mes.name == "LoginReply") {
            if (std::get<bool>(mes.variable_values.at(0))) {
                ZMQ.SendMessages("CommandResult", "", -1, {}, {}, {}, true);
            }
        }
    }
    else if (mes.topic == "CommunicationState") {
        // ui->label->setText(QString::fromStdString(mes.name));
    }
    else if (mes.topic == "ControlState") {
        // ui->label_2->setText(QString::fromStdString(mes.name));
    }
}

void MainController::StartCommunicationMiddleware()
{
    QString pid;
    int port = 10000; //ZMQ端口
    if (isPortInUse(port, pid)) {
        qDebug() << "Port" << port << "is in use by process" << pid;
        try {
            killProcess(pid);
        }
        catch (...) {
            Sleep(50);
        }
        qDebug() << "Process" << pid << "has been terminated.";
    }
    else {
        qDebug() << "Port" << port << "is not in use.";
    }
    KillService("EMService"); //关闭服务
    //设置外部程序
    QString program = "./ZMQ_Server/ZMQ_Server.exe";
    //启动外部程序
    communication_middleware_->setProcessChannelMode(QProcess::MergedChannels);
    communication_middleware_->start(program, QStringList(), QIODevice::ReadWrite);
    //检查外部程序是否成功启动
    if (communication_middleware_->waitForStarted()) {
        qDebug() << "External program started successfully.";
        //DEVICE_INFO->printDeviceSystemInfo("External program restarted successfully.");
    }
    else {
        qWarning() << "Failed to start external program.";
        //DEVICE_INFO->printDeviceSystemInfo("Failed to start external program.");
    }
}

void MainController::KillService(const QString& service_name)
{
    SC_HANDLE hSCManager = OpenSCManager(NULL, NULL, SC_MANAGER_ALL_ACCESS);
    if (!hSCManager) {
        qDebug() << "无法打开服务管理器: " << GetLastError();
        return;
    }
    // 打开指定的服务
    SC_HANDLE hService = OpenServiceW(hSCManager, (LPCWSTR)service_name.data(), SERVICE_STOP | SERVICE_QUERY_STATUS);
    if (!hService) {
        auto data = GetLastError();
        qDebug() << "无法打开服务: " << service_name << ", 错误代码: " << GetLastError();
        CloseServiceHandle(hSCManager); // 关闭服务管理器句柄
        return;
    }
    // 获取服务状态
    SERVICE_STATUS serviceStatus;
    if (!QueryServiceStatus(hService, &serviceStatus)) {
        qDebug() << "无法查询服务状态: " << service_name << ", 错误代码: " << GetLastError();
        CloseServiceHandle(hService);    // 关闭服务句柄
        CloseServiceHandle(hSCManager); // 关闭服务管理器句柄
        return;
    }
    // 检查服务是否已经停止
    if (serviceStatus.dwCurrentState == SERVICE_STOPPED) {
        qDebug() << "服务已处于停止状态: " << service_name;
        CloseServiceHandle(hService);    // 关闭服务句柄
        CloseServiceHandle(hSCManager); // 关闭服务管理器句柄
        return;
    }
    if (!ControlService(hService, SERVICE_CONTROL_STOP, &serviceStatus)) {
        qDebug() << "无法停止服务: " << service_name << ", 错误代码: " << GetLastError();
    }
    // 关闭句柄
    CloseServiceHandle(hService);   //关闭服务管理器句柄
    CloseServiceHandle(hSCManager); //关闭服务管理器句柄
}

bool MainController::isPortInUse(int port, QString& pid) {
    QProcess process;
    QString command = QString("netstat -ano | findstr %1").arg(port);

    process.start("cmd", QStringList() << "/C" << command);
    process.waitForFinished();
    QString output = process.readAllStandardOutput();

    if (!output.isEmpty()) {
        QStringList lines = output.split("\n", QString::SkipEmptyParts);
        for (const QString& line : lines) {
            if (line.contains(QString(":%1 ").arg(port))) {
                QStringList columns = line.split(QRegExp("\\s+"), QString::SkipEmptyParts);
                if (columns.size() >= 5) {
                    pid = columns.last();
                    return true;
                }
            }
        }
    }
    return false;
}
void MainController::killProcess(const QString& pid) {
    QProcess process;
    QString command = QString("taskkill /PID %1 /F").arg(pid);
    process.start(command);
    process.waitForFinished();
}

bool MainController::load()
{





    return false;
}

void MainController::RegisterCallBack(const std::string& id)
{
    Log("info", QString("RFID: %1").arg(QString::fromStdString(id)));
    //todo:接受RFID消息
}

void MainController::RFID_Connect(){
    asyn_tcp_client_->Connect();
}
