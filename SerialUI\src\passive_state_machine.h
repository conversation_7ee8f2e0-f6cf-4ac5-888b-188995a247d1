#ifndef PASSIVE_STATE_MACHINE_H
#define PASSIVE_STATE_MACHINE_H

#include <string>
#include <memory>
#include <iostream>
#include <vector>
#include "../../qserial/include/service.h"

/**
 * @brief 被动状态机类，用于处理AGV交互逻辑
 *
 * 该类负责管理与AGV的通信状态转换，通过ADAM-4055模块进行IO控制
 */
class PassiveStateMachine
{
public:
    // 输入信号枚举
    enum class InputSignal
    {
        kValid = 0,
        kCs0,
        kCs1,
        kNc1,
        kTrReq,
        kBusy,
        kCompt,
        kCont
    };

    // 输出信号枚举
    enum class OutputSignal
    {
        kLReq   = "#01100",
        kUReq   = "#01110",
        kNc1    = "#01120",
        kReady  = "#01130",
        kNc2    = "#01140",
        kNc3    = "#01150",
        kHoAvbl = "#01160",
        kEs     = "#01170"
    };

    // 状态枚举
    enum class State
    {
        kIdle,
        kWaitingForAgvRequestCmd,   // Got CS0, VALID
        kPreparingTransfer,         // Got L_REQ/TR_REQ CMD
        kReadyWaitingForAgvBusy,    // Sent PASSIVE_READY_STATUS ON
        kMonitoringTransfer,        // AGV is BUSY
        kWaitingForFinalAgvCleanup, // Sent PASSIVE_TRANSFER_COMPLETED & PASSIVE_READY_OFF
        kErrorState
    };
    /**
     * @brief ADAM-4055模块响应数据结构
     *
     * 用于存储和管理ADAM-4055模块的响应解析结果
     */
    struct Adam4055Data {
        bool command_was_valid = false;     // 响应是否以 '!' 开始
        bool is_error_response = false;     // 响应是否以 '?' 开始
        std::string module_address;         // 模块地址
        std::vector<bool> output_states;    // 8个DO状态 (CH0-CH7)
        std::vector<bool> input_states;     // 8个DI状态 (CH0-CH7)
        std::string raw_response;           // 原始响应
        std::string error_message;          // 错误信息

        /**
         * @brief 打印解析结果到控制台
         */
        void Print() const;

        /**
         * @brief 重置数据结构
         */
        void Reset();

        /**
         * @brief 检查数据是否有效
         * @return 数据有效返回true，否则返回false
         */
        bool IsValid() const;
    };

    // 常量定义
    static constexpr int DEFAULT_TIMEOUT = 1000;           // 默认超时时间(ms)
    static constexpr int MAX_CHANNELS = 8;                 // 最大通道数
    static constexpr const char* ADAM_QUERY_COMMAND = "#016\r";  // ADAM查询命令

public:
    /**
     * @brief 构造函数
     * @param serial_port 串口服务对象
     * @param port_name 串口名称（默认为空，使用配置文件）
     * @param baud_rate 波特率（默认9600）
     * @param data_bits 数据位（默认8）
     */
    explicit PassiveStateMachine(std::shared_ptr<SerialService> serial_port,
                                const std::string& port_name = "",
                                int baud_rate = 9600,
                                int data_bits = 8);

    /**
     * @brief 析构函数
     */
    ~PassiveStateMachine();

    // 禁用拷贝构造和赋值操作
    PassiveStateMachine(const PassiveStateMachine&) = delete;
    PassiveStateMachine& operator=(const PassiveStateMachine&) = delete;

    /**
     * @brief 获取当前状态
     * @return 当前状态枚举值
     */
    State GetCurrentState() const { return current_state_; }

    /**
     * @brief 获取当前状态名称
     * @return 状态名称字符串
     */
    std::string GetCurrentStateName() const;

    /**
     * @brief 处理状态机逻辑
     * @return 处理成功返回true，否则返回false
     */
    bool ProcessStateLogic();

    /**
     * @brief 重置状态机到初始状态
     */
    void Reset();

private:
    // 成员变量
    State current_state_;                               // 当前状态
    std::shared_ptr<SerialService> serial_port_;        // 串口服务对象
    Adam4055Data adam_data_;                            // ADAM数据
    std::string port_name_;                             // 串口名称
    int baud_rate_;                                     // 波特率
    int data_bits_;                                     // 数据位

    // 私有方法
    /**
     * @brief 解析ADAM-4055响应
     * @param response_str_with_cr 包含回车符的响应字符串
     * @return 解析结果
     */
    Adam4055Data ParseAdam4055Response(const std::string& response_str_with_cr);

    /**
     * @brief 将十六进制字节字符串转换为8位布尔值
     * @param hex_byte_str 十六进制字节字符串
     * @return 8位布尔值向量
     */
    std::vector<bool> HexByteStringTo8Bits(const std::string& hex_byte_str);

    /**
     * @brief 获取IO状态
     * @return 成功返回true，否则返回false
     */
    bool GetIoState();

    /**
     * @brief 设置L_REQ信号
     * @param state 信号状态（1为ON，0为OFF）
     * @return 成功返回true，否则返回false
     */
    bool SetLReq(int state);

    /**
     * @brief 设置READY信号
     * @param state 信号状态（1为ON，0为OFF）
     * @return 成功返回true，否则返回false
     */
    bool SetReady(int state);

    /**
     * @brief 格式化输出命令
     * @param signal 输出信号类型
     * @param state 信号状态
     * @return 格式化后的命令字符串
     */
    std::string FormatOutputCommand(OutputSignal signal, int state);

    /**
     * @brief 验证ADAM输出确认
     * @param response_str 响应字符串
     * @return 确认有效返回true，否则返回false
     */
    bool IsAdamOutputAck(const std::string& response_str);

    /**
     * @brief 初始化串口连接
     * @return 成功返回true，否则返回false
     */
    bool InitializeSerialPort();

    /**
     * @brief 处理错误状态
     * @param error_message 错误信息
     */
    void HandleError(const std::string& error_message);
};

#endif // PASSIVE_STATE_MACHINE_H
